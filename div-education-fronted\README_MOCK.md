# 🎭 DIV教育平台 - Mock模拟数据启动指南

## ✅ Mock模式已成功配置

您的DIV教育学习平台现在已经支持Mock模拟数据模式，可以在不启动后端服务的情况下运行前端应用。

## 🚀 快速启动

### 方式一：使用启动脚本（推荐）

**Windows用户：**
```bash
# 双击运行或在命令行执行
start-mock.bat
```

**Linux/Mac用户：**
```bash
# 在终端执行
./start-mock.sh
```

### 方式二：使用npm命令

```bash
# 启动Mock模式
npm run dev:mock

# 或者设置环境变量后启动
npm run dev
```

## 🌐 访问地址

启动成功后，访问以下地址：
- **主要地址**: http://localhost:5176
- **备用地址**: 如果5176端口被占用，会自动切换到其他端口

## 🎮 测试账号

Mock模式下，以下账号可以直接登录（任意密码）：

| 用户名 | 邮箱 | 角色 | 说明 |
|--------|------|------|------|
| 张三 | <EMAIL> | 学生 | 高级学员，120学时 |
| 李老师 | <EMAIL> | 教师 | 资深讲师 |
| 王小明 | <EMAIL> | 学生 | 中级学员，85学时 |

## 📊 Mock数据内容

### 🎯 完整功能支持
- ✅ **用户系统**: 登录、注册、个人资料
- ✅ **课程管理**: 15门课程，5个分类
- ✅ **社区功能**: 10个帖子，完整评论系统
- ✅ **仿真实验**: 6个物理化学实验
- ✅ **学习记录**: 进度跟踪，学习分析
- ✅ **数据分析**: 能力评估，学习路径推荐

### 📈 数据统计
- **用户数据**: 3个示例用户
- **课程数据**: 15门课程（前端、后端、移动、数据科学、设计）
- **社区数据**: 10个帖子 + 相关评论
- **实验数据**: 6个仿真实验（物理、化学）
- **学习数据**: 完整的学习记录和分析报告

## 🔧 技术特性

### Mock模式特点
- 🎭 **无需后端**: 完全使用前端模拟数据
- ⚡ **快速启动**: 无需数据库和后端服务
- 🔄 **实时更新**: 支持热重载，修改即生效
- 📱 **完整体验**: 所有功能都有对应的Mock实现
- 🕐 **模拟延迟**: 500ms网络延迟，模拟真实环境

### 环境变量控制
```bash
# 启用Mock模式
VITE_USE_MOCK=true

# 禁用Mock模式（使用真实API）
VITE_USE_MOCK=false
```

## 🔄 模式切换

### 切换到Mock模式
1. 修改 `.env.development` 文件：`VITE_USE_MOCK=true`
2. 重启开发服务器
3. 或者在URL中添加参数：`?mock=true`

### 切换到API模式
1. 修改 `.env.development` 文件：`VITE_USE_MOCK=false`
2. 启动后端服务：`cd ../div-education-backend && mvn spring-boot:run`
3. 重启前端服务

## 🐛 故障排除

### 常见问题

**1. Mock模式不生效**
- 检查浏览器控制台是否显示Mock模式提示
- 确认环境变量 `VITE_USE_MOCK=true`
- 重启开发服务器

**2. 端口被占用**
- 系统会自动切换到可用端口
- 查看终端输出的实际访问地址

**3. 登录失败**
- Mock模式下任意密码都可以登录
- 确保使用提供的测试邮箱地址

**4. 数据不显示**
- 检查浏览器控制台错误信息
- 确认Mock数据文件完整性

## 📁 相关文件

```
div-education-fronted/
├── src/
│   ├── api/
│   │   └── index.js          # API配置和Mock切换逻辑
│   └── mock/
│       ├── api.js            # Mock API接口实现
│       ├── data.js           # Mock数据定义
│       └── server.js         # 独立Mock服务器（可选）
├── .env.development          # 环境变量配置
├── start-mock.bat           # Windows启动脚本
├── start-mock.sh            # Linux/Mac启动脚本
└── MOCK_SETUP.md            # 详细配置说明
```

## 🎉 开始使用

现在您可以：
1. 运行 `start-mock.bat`（Windows）或 `./start-mock.sh`（Linux/Mac）
2. 打开浏览器访问 http://localhost:5176
3. 使用测试账号登录体验完整功能
4. 无需启动后端服务和数据库

享受您的DIV教育学习平台Mock体验！🎓
