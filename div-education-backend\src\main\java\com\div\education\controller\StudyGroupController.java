package com.div.education.controller;

import com.div.education.dto.ApiResponse;
import com.div.education.dto.PageResponse;
import com.div.education.entity.StudyGroup;
import com.div.education.repository.StudyGroupRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学习小组控制器
 */
@RestController
@RequestMapping("/study-groups")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class StudyGroupController {

    private final StudyGroupRepository studyGroupRepository;

    /**
     * 获取学习小组列表
     */
    @GetMapping
    public ApiResponse<PageResponse<StudyGroup>> getStudyGroups(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String category) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<StudyGroup> studyGroupPage;
        
        if (category != null) {
            studyGroupPage = studyGroupRepository.findByCategory(category, pageable);
        } else {
            studyGroupPage = studyGroupRepository.findByIsPublicTrueAndStatus(StudyGroup.StudyGroupStatus.ACTIVE, pageable);
        }
        
        return ApiResponse.success(PageResponse.of(studyGroupPage));
    }

    /**
     * 根据ID获取学习小组
     */
    @GetMapping("/{id}")
    public ApiResponse<StudyGroup> getStudyGroupById(@PathVariable Long id) {
        return studyGroupRepository.findById(id)
                .map(ApiResponse::success)
                .orElse(ApiResponse.notFound("学习小组不存在"));
    }

    /**
     * 搜索学习小组
     */
    @GetMapping("/search")
    public ApiResponse<PageResponse<StudyGroup>> searchStudyGroups(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<StudyGroup> studyGroupPage = studyGroupRepository.searchStudyGroups(keyword, pageable);
        return ApiResponse.success(PageResponse.of(studyGroupPage));
    }

    /**
     * 获取热门学习小组
     */
    @GetMapping("/popular")
    public ApiResponse<List<StudyGroup>> getPopularStudyGroups(
            @RequestParam(defaultValue = "10") int limit) {
        
        Pageable pageable = PageRequest.of(0, limit);
        List<StudyGroup> studyGroups = studyGroupRepository.findPopularStudyGroups(pageable);
        return ApiResponse.success(studyGroups);
    }

    /**
     * 获取最新学习小组
     */
    @GetMapping("/latest")
    public ApiResponse<List<StudyGroup>> getLatestStudyGroups(
            @RequestParam(defaultValue = "10") int limit) {
        
        Pageable pageable = PageRequest.of(0, limit);
        List<StudyGroup> studyGroups = studyGroupRepository.findLatestStudyGroups(pageable);
        return ApiResponse.success(studyGroups);
    }

    /**
     * 获取推荐学习小组
     */
    @GetMapping("/recommended")
    public ApiResponse<List<StudyGroup>> getRecommendedStudyGroups(
            @RequestParam(defaultValue = "10") int limit) {
        
        Pageable pageable = PageRequest.of(0, limit);
        List<StudyGroup> studyGroups = studyGroupRepository.findRecommendedStudyGroups(pageable);
        return ApiResponse.success(studyGroups);
    }

    /**
     * 根据分类统计学习小组数量
     */
    @GetMapping("/stats/by-category")
    public ApiResponse<List<Object[]>> getStudyGroupStatsByCategory() {
        List<Object[]> stats = studyGroupRepository.countStudyGroupsByCategory();
        return ApiResponse.success(stats);
    }

    /**
     * 创建学习小组
     */
    @PostMapping
    public ApiResponse<StudyGroup> createStudyGroup(@RequestBody StudyGroup studyGroup) {
        try {
            studyGroup.setStatus(StudyGroup.StudyGroupStatus.ACTIVE);
            studyGroup.setCurrentMembers(1); // 创建者自动成为第一个成员
            StudyGroup savedStudyGroup = studyGroupRepository.save(studyGroup);
            return ApiResponse.success("学习小组创建成功", savedStudyGroup);
        } catch (Exception e) {
            return ApiResponse.error("学习小组创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新学习小组
     */
    @PutMapping("/{id}")
    public ApiResponse<StudyGroup> updateStudyGroup(@PathVariable Long id, @RequestBody StudyGroup studyGroup) {
        try {
            if (!studyGroupRepository.existsById(id)) {
                return ApiResponse.notFound("学习小组不存在");
            }
            studyGroup.setId(id);
            StudyGroup updatedStudyGroup = studyGroupRepository.save(studyGroup);
            return ApiResponse.success("学习小组更新成功", updatedStudyGroup);
        } catch (Exception e) {
            return ApiResponse.error("学习小组更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除学习小组
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteStudyGroup(@PathVariable Long id) {
        try {
            if (!studyGroupRepository.existsById(id)) {
                return ApiResponse.notFound("学习小组不存在");
            }
            studyGroupRepository.deleteById(id);
            return ApiResponse.success("学习小组删除成功", null);
        } catch (Exception e) {
            return ApiResponse.error("学习小组删除失败: " + e.getMessage());
        }
    }

    /**
     * 加入学习小组
     */
    @PostMapping("/{id}/join")
    public ApiResponse<StudyGroup> joinStudyGroup(@PathVariable Long id) {
        try {
            StudyGroup studyGroup = studyGroupRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("学习小组不存在"));
            
            if (studyGroup.getCurrentMembers() >= studyGroup.getMaxMembers()) {
                return ApiResponse.error("学习小组已满员");
            }
            
            studyGroup.setCurrentMembers(studyGroup.getCurrentMembers() + 1);
            StudyGroup updatedStudyGroup = studyGroupRepository.save(studyGroup);
            return ApiResponse.success("加入学习小组成功", updatedStudyGroup);
        } catch (Exception e) {
            return ApiResponse.error("加入学习小组失败: " + e.getMessage());
        }
    }

    /**
     * 退出学习小组
     */
    @PostMapping("/{id}/leave")
    public ApiResponse<StudyGroup> leaveStudyGroup(@PathVariable Long id) {
        try {
            StudyGroup studyGroup = studyGroupRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("学习小组不存在"));
            
            if (studyGroup.getCurrentMembers() > 0) {
                studyGroup.setCurrentMembers(studyGroup.getCurrentMembers() - 1);
            }
            
            StudyGroup updatedStudyGroup = studyGroupRepository.save(studyGroup);
            return ApiResponse.success("退出学习小组成功", updatedStudyGroup);
        } catch (Exception e) {
            return ApiResponse.error("退出学习小组失败: " + e.getMessage());
        }
    }
}
