// API配置 - DIV教育学习平台
// 支持Mock模式和后端API模式切换

// 检查是否启用Mock模式
const USE_MOCK = import.meta.env.VITE_USE_MOCK === 'true' || window.location.search.includes('mock=true')

// API基础配置
const API_BASE_URL = 'http://localhost:8081/api'

// 导入Mock API
import { userAPI, courseAPI, communityAPI, groupAPI, newsAPI, learningAPI, experimentAPI, analyticsAPI } from '../mock/api.js'

// HTTP请求工具
const request = async (url, options = {}) => {
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  }
  
  if (config.body && typeof config.body === 'object') {
    config.body = JSON.stringify(config.body)
  }
  
  try {
    const response = await fetch(`${API_BASE_URL}${url}`, config)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    // 转换后端响应格式为前端期望格式
    if (data.code === 200) {
      // 如果是分页数据，转换格式
      if (data.data && data.data.content !== undefined) {
        return {
          code: 200,
          message: data.message || 'success',
          data: {
            list: data.data.content,
            total: data.data.totalElements,
            page: data.data.page + 1, // 后端0基索引转为1基索引
            pageSize: data.data.size
          },
          timestamp: new Date().toISOString()
        }
      }

      return {
        code: 200,
        message: data.message || 'success',
        data: data.data,
        timestamp: new Date().toISOString()
      }
    } else {
      throw new Error(data.message || '请求失败')
    }
  } catch (error) {
    console.error('❌ 后端API请求失败:', error.message)
    console.error('🔧 请确保后端服务已启动: http://localhost:8081/api')
    console.error('📋 启动命令: mvn spring-boot:run')

    // 显示用户友好的错误信息
    const errorMessage = error.message.includes('fetch')
      ? '无法连接到后端服务，请确保后端已启动'
      : `后端服务错误: ${error.message}`

    throw new Error(errorMessage)
  }
}



// Mock API适配器
const createMockAdapter = (mockAPI, methodMap) => {
  const adapter = {}
  for (const [key, mockMethod] of Object.entries(methodMap)) {
    adapter[key] = (...args) => {
      if (USE_MOCK) {
        console.log(`🎭 使用Mock数据: ${key}`, args)
        return mockAPI[mockMethod](...args)
      } else {
        // 使用原始的后端API调用
        return methodMap[key](...args)
      }
    }
  }
  return adapter
}

// 导出统一的API接口
export const api = {
  // 认证相关
  auth: USE_MOCK ? {
    login: (email, password) => {
      console.log('🎭 使用Mock登录')
      return userAPI.login(email, password)
    },
    register: (username, email, password, nickname, role) => {
      console.log('🎭 使用Mock注册')
      return userAPI.register({ username, email, password, nickname, role })
    },
    checkEmail: (email) => {
      console.log('🎭 使用Mock检查邮箱')
      return Promise.resolve({ code: 200, message: 'success', data: { available: true } })
    },
    checkUsername: (username) => {
      console.log('🎭 使用Mock检查用户名')
      return Promise.resolve({ code: 200, message: 'success', data: { available: true } })
    }
  } : {
    login: (email, password) => request('/auth/login', {
      method: 'POST',
      body: { email, password }
    }),
    register: (username, email, password, nickname, role) => request('/auth/register', {
      method: 'POST',
      body: { username, email, password, nickname, role }
    }),
    checkEmail: (email) => request(`/auth/check-email?email=${email}`),
    checkUsername: (username) => request(`/auth/check-username?username=${username}`)
  },

  // 用户相关
  users: USE_MOCK ? {
    getUsers: (page = 0, size = 10) => {
      console.log('🎭 使用Mock获取用户列表')
      return userAPI.getProfile(1) // 简化实现
    },
    getUserById: (id) => {
      console.log('🎭 使用Mock获取用户信息')
      return userAPI.getProfile(id)
    },
    searchUsers: (keyword, page = 0, size = 10) => {
      console.log('🎭 使用Mock搜索用户')
      return userAPI.getProfile(1) // 简化实现
    },
    updateUser: (id, data) => {
      console.log('🎭 使用Mock更新用户')
      return userAPI.updateProfile(id, data)
    }
  } : {
    getUsers: (page = 0, size = 10) => request(`/users?page=${page}&size=${size}`),
    getUserById: (id) => request(`/users/${id}`),
    searchUsers: (keyword, page = 0, size = 10) => request(`/users/search?keyword=${keyword}&page=${page}&size=${size}`),
    updateUser: (id, data) => request(`/users/${id}`, {
      method: 'PUT',
      body: data
    })
  },

  // 课程相关
  courses: USE_MOCK ? {
    getCourses: (page = 0, size = 10, category, level) => {
      console.log('🎭 使用Mock获取课程列表')
      return courseAPI.getCourses({ page: page + 1, pageSize: size, categoryId: category })
    },
    getCourseById: (id) => {
      console.log('🎭 使用Mock获取课程详情')
      return courseAPI.getCourseDetail(id)
    },
    searchCourses: (keyword, page = 0, size = 10) => {
      console.log('🎭 使用Mock搜索课程')
      return courseAPI.getCourses({ keyword, page: page + 1, pageSize: size })
    },
    getPopularCourses: (limit = 10) => {
      console.log('🎭 使用Mock获取热门课程')
      return courseAPI.getCourses({ pageSize: limit })
    },
    getLatestCourses: (limit = 10) => {
      console.log('🎭 使用Mock获取最新课程')
      return courseAPI.getCourses({ pageSize: limit })
    },
    getRecommendedCourses: (minRating = 4.0, limit = 10) => {
      console.log('🎭 使用Mock获取推荐课程')
      return courseAPI.getCourses({ pageSize: limit })
    },
    getCategories: () => {
      console.log('🎭 使用Mock获取课程分类')
      return courseAPI.getCategories()
    }
  } : {
    getCourses: (page = 0, size = 10, category, level) => {
      let url = `/courses?page=${page}&size=${size}`
      if (category) url += `&category=${category}`
      if (level) url += `&level=${level}`
      return request(url)
    },
    getCourseById: (id) => request(`/courses/${id}`),
    searchCourses: (keyword, page = 0, size = 10) => request(`/courses/search?keyword=${keyword}&page=${page}&size=${size}`),
    getPopularCourses: (limit = 10) => request(`/courses/popular?limit=${limit}`),
    getLatestCourses: (limit = 10) => request(`/courses/latest?limit=${limit}`),
    getRecommendedCourses: (minRating = 4.0, limit = 10) => request(`/courses/recommended?minRating=${minRating}&limit=${limit}`)
  },

  // 实验相关
  experiments: USE_MOCK ? {
    getExperiments: (page = 0, size = 10, subject, difficulty) => {
      console.log('🎭 使用Mock获取实验列表')
      return experimentAPI.getExperiments({ subject, difficulty })
    },
    getExperimentById: (id) => {
      console.log('🎭 使用Mock获取实验详情')
      return experimentAPI.getExperimentDetail(id)
    },
    searchExperiments: (keyword, page = 0, size = 10) => {
      console.log('🎭 使用Mock搜索实验')
      return experimentAPI.getExperiments({ keyword })
    },
    getPopularExperiments: (limit = 10) => {
      console.log('🎭 使用Mock获取热门实验')
      return experimentAPI.getExperiments({})
    },
    getLatestExperiments: (limit = 10) => {
      console.log('🎭 使用Mock获取最新实验')
      return experimentAPI.getExperiments({})
    }
  } : {
    getExperiments: (page = 0, size = 10, subject, difficulty) => {
      let url = `/experiments?page=${page}&size=${size}`
      if (subject) url += `&subject=${subject}`
      if (difficulty) url += `&difficulty=${difficulty}`
      return request(url)
    },
    getExperimentById: (id) => request(`/experiments/${id}`),
    searchExperiments: (keyword, page = 0, size = 10) => request(`/experiments/search?keyword=${keyword}&page=${page}&size=${size}`),
    getPopularExperiments: (limit = 10) => request(`/experiments/popular?limit=${limit}`),
    getLatestExperiments: (limit = 10) => request(`/experiments/latest?limit=${limit}`)
  },

  // 帖子相关
  posts: USE_MOCK ? {
    getPosts: (page = 0, size = 10, category) => {
      console.log('🎭 使用Mock获取帖子列表')
      return communityAPI.getPosts({ page: page + 1, pageSize: size })
    },
    getPostById: (id) => {
      console.log('🎭 使用Mock获取帖子详情')
      return communityAPI.getPostDetail(id)
    },
    searchPosts: (keyword, page = 0, size = 10) => {
      console.log('🎭 使用Mock搜索帖子')
      return communityAPI.getPosts({ keyword, page: page + 1, pageSize: size })
    },
    getPopularPosts: (limit = 10) => {
      console.log('🎭 使用Mock获取热门帖子')
      return communityAPI.getPosts({ pageSize: limit })
    },
    getLatestPosts: (limit = 10) => {
      console.log('🎭 使用Mock获取最新帖子')
      return communityAPI.getPosts({ pageSize: limit })
    }
  } : {
    getPosts: (page = 0, size = 10, category) => {
      let url = `/posts?page=${page}&size=${size}`
      if (category) url += `&category=${category}`
      return request(url)
    },
    getPostById: (id) => request(`/posts/${id}`),
    searchPosts: (keyword, page = 0, size = 10) => request(`/posts/search?keyword=${keyword}&page=${page}&size=${size}`),
    getPopularPosts: (limit = 10) => request(`/posts/popular?limit=${limit}`),
    getLatestPosts: (limit = 10) => request(`/posts/latest?limit=${limit}`)
  },

  // 学习小组相关
  studyGroups: USE_MOCK ? {
    getStudyGroups: (page = 0, size = 10, category) => {
      console.log('🎭 使用Mock获取学习小组列表')
      return groupAPI.getGroups({ page: page + 1, pageSize: size })
    },
    getStudyGroupById: (id) => {
      console.log('🎭 使用Mock获取学习小组详情')
      return groupAPI.getGroupDetail(id)
    },
    searchStudyGroups: (keyword, page = 0, size = 10) => {
      console.log('🎭 使用Mock搜索学习小组')
      return groupAPI.getGroups({ keyword, page: page + 1, pageSize: size })
    },
    getPopularStudyGroups: (limit = 10) => {
      console.log('🎭 使用Mock获取热门学习小组')
      return groupAPI.getGroups({ pageSize: limit })
    },
    getLatestStudyGroups: (limit = 10) => {
      console.log('🎭 使用Mock获取最新学习小组')
      return groupAPI.getGroups({ pageSize: limit })
    }
  } : {
    getStudyGroups: (page = 0, size = 10, category) => {
      let url = `/study-groups?page=${page}&size=${size}`
      if (category) url += `&category=${category}`
      return request(url)
    },
    getStudyGroupById: (id) => request(`/study-groups/${id}`),
    searchStudyGroups: (keyword, page = 0, size = 10) => request(`/study-groups/search?keyword=${keyword}&page=${page}&size=${size}`),
    getPopularStudyGroups: (limit = 10) => request(`/study-groups/popular?limit=${limit}`),
    getLatestStudyGroups: (limit = 10) => request(`/study-groups/latest?limit=${limit}`)
  }
}

// 初始化提示
if (USE_MOCK) {
  console.log('🎭 ========================================')
  console.log('🎭 DIV教育平台 - Mock模式已启用')
  console.log('🎭 ========================================')
  console.log('🎭 当前使用模拟数据，无需启动后端服务')
  console.log('🎭 要切换到真实API，请修改环境变量:')
  console.log('🎭 VITE_USE_MOCK=false 或移除 ?mock=true 参数')
  console.log('🎭 ========================================')
} else {
  console.log('🔗 ========================================')
  console.log('🔗 DIV教育平台 - 后端API模式')
  console.log('🔗 ========================================')
  console.log('🔗 当前连接后端服务:', API_BASE_URL)
  console.log('🔗 要切换到Mock模式，请设置环境变量:')
  console.log('🔗 VITE_USE_MOCK=true 或添加 ?mock=true 参数')
  console.log('🔗 ========================================')
}

// 默认导出API对象
export default api
