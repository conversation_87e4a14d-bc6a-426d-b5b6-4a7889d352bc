package com.div.education.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 学习小组实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "study_groups")
public class StudyGroup extends BaseEntity {

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "cover_image", length = 500)
    private String coverImage;

    @Column(name = "category", length = 50)
    private String category;

    @Column(name = "max_members", nullable = false)
    private Integer maxMembers = 100;

    @Column(name = "current_members", nullable = false)
    private Integer currentMembers = 0;

    @Column(name = "is_public", nullable = false)
    private Boolean isPublic = true;

    @Column(name = "tags", columnDefinition = "TEXT")
    private String tags; // 标签（逗号分隔）

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private StudyGroupStatus status = StudyGroupStatus.ACTIVE;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creator_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private User creator;

    /**
     * 学习小组状态枚举
     */
    public enum StudyGroupStatus {
        ACTIVE("活跃"),
        INACTIVE("不活跃"),
        ARCHIVED("已归档");

        private final String description;

        StudyGroupStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
