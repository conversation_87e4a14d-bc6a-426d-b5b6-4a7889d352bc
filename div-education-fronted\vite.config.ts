import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
console.log('✅ Vite config loaded. allowedHosts:', 'all');

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    host: '0.0.0.0', // 必须：监听所有接口，允许外部访问
    port: 5175,
    strictPort: false, // 允许端口自动切换
    // 👇 添加 allowedHosts
    allowedHosts: true,
      // [
      //   // 'https://44f65e11.r7.cpolar.top/',  // 允许这个域名
      //   // 也可以写成 'all' 来允许所有主机（仅开发环境可用，不推荐生产）

      // ],
  }
})