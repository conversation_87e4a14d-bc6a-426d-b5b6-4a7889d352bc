# Mock数据启动指南

## 🎭 Mock模式说明

DIV教育学习平台支持两种数据模式：
- **Mock模式**: 使用本地模拟数据，无需后端服务
- **API模式**: 连接真实后端服务

## 🚀 启动Mock模式

### 方式一：环境变量控制（推荐）

1. **修改环境变量文件**
   ```bash
   # 编辑 .env.development 文件
   VITE_USE_MOCK=true
   ```

2. **启动前端服务**
   ```bash
   cd div-education-fronted
   npm run dev
   ```

3. **访问应用**
   ```
   http://localhost:3000
   ```

### 方式二：URL参数控制

直接在浏览器中访问：
```
http://localhost:3000?mock=true
```

## 🎯 Mock数据特性

### 📊 包含的模拟数据
- **用户数据**: 3个示例用户（学生、教师、管理员）
- **课程数据**: 15门课程，涵盖5个分类
- **社区数据**: 10个帖子和相关评论
- **实验数据**: 6个仿真实验
- **学习记录**: 完整的学习进度数据
- **数据分析**: 学习分析和能力评估报告

### 🔧 Mock功能支持
- ✅ 用户登录/注册
- ✅ 课程浏览/搜索/分类
- ✅ 社区帖子浏览/发布
- ✅ 实验列表/详情
- ✅ 学习进度记录
- ✅ 数据分析报告
- ✅ 模拟网络延迟（500ms）

## 🎮 Mock测试账号

| 用户名 | 邮箱 | 密码 | 角色 |
|--------|------|------|------|
| 张三 | <EMAIL> | 任意密码 | 学生 |
| 李老师 | <EMAIL> | 任意密码 | 教师 |
| 王小明 | <EMAIL> | 任意密码 | 学生 |

> 注意：Mock模式下，任何密码都可以登录成功

## 🔄 切换模式

### 切换到Mock模式
```bash
# 方式1: 修改环境变量
echo "VITE_USE_MOCK=true" > .env.development

# 方式2: URL参数
# 访问 http://localhost:3000?mock=true
```

### 切换到API模式
```bash
# 修改环境变量
echo "VITE_USE_MOCK=false" > .env.development

# 确保后端服务已启动
cd ../div-education-backend
mvn spring-boot:run
```

## 🐛 故障排除

### Mock模式不生效
1. 检查环境变量文件 `.env.development`
2. 重启开发服务器 `npm run dev`
3. 检查浏览器控制台是否显示Mock模式提示

### 数据不显示
1. 检查浏览器控制台错误信息
2. 确认mock数据文件 `src/mock/data.js` 存在
3. 检查API调用是否正确映射到mock函数

### 网络请求错误
- Mock模式下不会发送真实网络请求
- 如果看到网络错误，说明可能未正确切换到Mock模式

## 📝 开发说明

Mock数据文件位置：
- `src/mock/api.js` - Mock API接口实现
- `src/mock/data.js` - Mock数据定义
- `src/api/index.js` - API配置和模式切换逻辑

修改Mock数据：
1. 编辑 `src/mock/data.js` 文件
2. 修改相应的数据数组
3. 保存后自动生效（热重载）
