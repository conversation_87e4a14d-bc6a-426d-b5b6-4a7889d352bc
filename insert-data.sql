-- 插入用户数据
INSERT INTO users (username, email, password, nickname, role, status, bio) VALUES
('teacher1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'Teacher Li', 'TEACHER', 'ACTIVE', 'Frontend Developer'),
('teacher2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'Teacher Wang', 'TEACHER', 'ACTIVE', 'Backend Expert'),
('teacher3', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'Teacher Chen', 'TEACHER', 'ACTIVE', 'Data Science'),
('student1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'Zhang', 'STUDENT', 'ACTIVE', 'CS Student'),
('student2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'Li', 'STUDENT', 'ACTIVE', 'SE Student'),
('student3', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'Wang', 'STUDENT', 'ACTIVE', 'DS Student');

-- 插入课程数据
INSERT INTO courses (title, description, instructor, category, level, duration, price, original_price, rating, student_count, lesson_count, status, tags, teacher_id) VALUES
('Vue.js 3.0 Complete Course', 'Learn Vue.js 3.0 from scratch', 'Teacher Li', 'Frontend', 'INTERMEDIATE', 1200, 199.00, 299.00, 4.8, 1234, 24, 'PUBLISHED', 'Vue.js,Frontend,JavaScript', 2),
('React Modern Development', 'Learn React with latest features', 'Teacher Li', 'Frontend', 'INTERMEDIATE', 1500, 249.00, 349.00, 4.7, 967, 30, 'PUBLISHED', 'React,Frontend,JavaScript', 2),
('Spring Boot Microservices', 'Build enterprise microservices', 'Teacher Wang', 'Backend', 'ADVANCED', 1800, 299.00, 399.00, 4.9, 856, 36, 'PUBLISHED', 'Spring Boot,Java,Backend', 3),
('Python Data Analysis', 'Data analysis with Python', 'Teacher Chen', 'Data Science', 'BEGINNER', 900, 149.00, 199.00, 4.6, 1456, 18, 'PUBLISHED', 'Python,Data Analysis', 4);

-- 插入实验数据
INSERT INTO experiments (title, description, subject, difficulty, duration, rating, completed_count, objectives, equipment, steps, tags, status) VALUES
('Pendulum Motion Experiment', 'Study pendulum motion laws', 'Physics', 'BEGINNER', 30, 4.7, 1234, 'Understand pendulum motion', 'Pendulum,Timer,Ruler', 'Setup,Measure,Record,Analyze', 'Physics,Motion', 'ACTIVE'),
('Light Refraction Experiment', 'Verify refraction laws', 'Physics', 'BEGINNER', 25, 4.6, 756, 'Verify refraction law', 'Laser,Glass block,Protractor', 'Setup,Adjust,Observe,Measure', 'Physics,Optics', 'ACTIVE'),
('Acid-Base Titration', 'Learn titration technique', 'Chemistry', 'INTERMEDIATE', 45, 4.8, 987, 'Master titration skills', 'Burette,Flask,Indicator', 'Prepare,Fill,Titrate,Calculate', 'Chemistry,Analysis', 'ACTIVE');

-- 插入帖子数据
INSERT INTO posts (title, content, category, tags, view_count, like_count, comment_count, is_pinned, status, author_id) VALUES
('Vue 3.0 Learning Experience', 'Sharing my Vue 3.0 learning journey and tips', 'Learning', 'Vue.js,Frontend,Learning', 1234, 89, 23, TRUE, 'PUBLISHED', 5),
('How to Learn Programming Effectively', 'Tips and methods for effective programming learning', 'Learning', 'Programming,Learning,Tips', 1567, 98, 31, FALSE, 'PUBLISHED', 6),
('Spring Boot Best Practices', 'Sharing Spring Boot development experience', 'Technology', 'Spring Boot,Java,Backend', 987, 67, 18, FALSE, 'PUBLISHED', 6);

-- 插入学习小组数据
INSERT INTO study_groups (name, description, category, max_members, current_members, is_public, tags, status, creator_id) VALUES
('Frontend Tech Group', 'Frontend technology discussion and learning', 'Frontend', 100, 67, TRUE, 'Frontend,Vue,React,Tech', 'ACTIVE', 5),
('Java Backend Group', 'Java backend technology learning group', 'Backend', 80, 54, TRUE, 'Java,Spring Boot,Backend', 'ACTIVE', 6),
('Algorithm Study Group', 'Algorithm and data structure practice', 'Algorithm', 150, 89, TRUE, 'Algorithm,Data Structure,Practice', 'ACTIVE', 7);

-- 插入新闻数据
INSERT INTO news (title, summary, content, category, tags, source, author, view_count, like_count, is_hot, status, published_at) VALUES
('Vue 3.4 Released', 'Vue.js 3.4 brings major performance improvements', 'Vue.js 3.4 version officially released with significant improvements in compiler optimization and reactive system.', 'Frontend Tech', 'Vue.js,Frontend,Update', 'Vue Official', 'Vue Team', 2345, 156, TRUE, 'PUBLISHED', '2024-01-15 10:00:00'),
('React 18.3 Released', 'React team releases 18.3 with bug fixes', 'React 18.3 focuses on bug fixes and stability improvements.', 'Frontend Tech', 'React,Frontend,Update', 'React Official', 'React Team', 1876, 123, TRUE, 'PUBLISHED', '2024-01-12 14:20:00'),
('Spring Boot 3.2 Released', 'Spring Boot 3.2 supports virtual threads', 'Spring Boot 3.2 brings full support for Java 21 virtual threads.', 'Backend Tech', 'Spring Boot,Java,Virtual Threads', 'Spring Official', 'Spring Team', 1876, 98, TRUE, 'PUBLISHED', '2024-01-10 14:30:00');

-- 插入学习记录数据
INSERT INTO learning_records (user_id, course_id, progress, last_position, completed_at) VALUES
(5, 1, 75, 18, NULL),
(5, 2, 30, 9, NULL),
(6, 3, 90, 32, NULL),
(6, 1, 45, 11, NULL),
(7, 4, 85, 15, NULL),
(7, 1, 100, 24, '2024-01-20 15:30:00');
