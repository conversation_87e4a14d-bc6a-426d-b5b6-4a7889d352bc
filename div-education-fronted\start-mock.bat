@echo off
echo.
echo ========================================
echo   DIV教育学习平台 - Mock模式启动
echo ========================================
echo.
echo 🎭 正在启动Mock模拟数据模式...
echo 📁 工作目录: %CD%
echo.

REM 检查node_modules是否存在
if not exist "node_modules" (
    echo ⚠️  检测到依赖未安装，正在安装...
    call npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

echo 🚀 启动前端服务（Mock模式）...
echo.
echo 📋 启动信息:
echo    - 模式: Mock模拟数据
echo    - 端口: 5176 (如被占用会自动切换)
echo    - 地址: http://localhost:5176
echo.
echo 🎮 测试账号:
echo    - 邮箱: <EMAIL> (任意密码)
echo    - 邮箱: <EMAIL> (任意密码)
echo    - 邮箱: <EMAIL> (任意密码)
echo.
echo 💡 提示: 按 Ctrl+C 停止服务
echo ========================================
echo.

REM 启动开发服务器
call npm run dev:mock

echo.
echo 🛑 服务已停止
pause
